import { API_URL } from '../constants'
import { LANGUAGES, englishLanguageId } from 'src/v2/constants/languages'
import { getAccessToken } from '../Cookies'
import moment from 'moment'
import 'moment/locale/en-gb'
import queryString from 'query-string'

export const getDescriptionErrors = (dataErrors) => {
  const errors = dataErrors.reduce((errors, data) => {
    return [...errors, ...data.errors]
  }, [])
  return errors
}

export const getQueryStringParams = (search) => {
  const params = queryString.parse(search)
  return params
}

export const isUserGPM = (roles) => {
  if (!roles) {
    return false
  }

  const rolesGPM = ['CCF', 'DCF', 'IIPAM', 'SPCF']
  const rolesFormat = roles.split(',')

  return rolesGPM.some((role) => rolesFormat.includes(role))
}

export const convertToDataModal = (assetDescription) => {
  let assetDescriptionOriginal = assetDescription
  assetDescriptionOriginal = assetDescriptionOriginal.replace(/\r\n|\n|\r/gm, '<br>')
  assetDescriptionOriginal = assetDescriptionOriginal.replace(/"/gm, "'")
  const string = assetDescriptionOriginal.match(/\*\*\{[^}]*\}\}*.[^}]*\}\}/g)

  if (string !== null) {
    string.map((element, index) => {
      const splitToModal = string[index].split(/{{/)

      let title = splitToModal[1].replace(/}\}/, '')
      let content = splitToModal[2].replace(/\*\*/, '')
      content = content.replace(/\}\}/, '')

      const modalLink = `<a class="link-modal" href="#" data-modal="${content}">${title}</a>`

      let replaced = assetDescriptionOriginal.replace(/\*\*\{[^}]*\}\}*.[^}]*\}\}/, modalLink)
      replaced = replaced.replace(/\*\*/, '')

      assetDescriptionOriginal = replaced
    })
    return assetDescriptionOriginal.replace(/<br>/gm, '\n')
  }
}

export const getLanguageNameById = (id) => {
  const _temp = LANGUAGES.filter((language) => language.id === parseInt(id))
  return _temp.length > 0 ? _temp[0].description : ''
}

export const getLanguageCodeById = (id) => {
  const _temp = LANGUAGES.filter((language) => language.id === parseInt(id))
  return _temp.length > 0 ? _temp[0].code : 'en'
}

export const splitIfIsURLS3 = (url) => {
  if (url && url.includes('s3://')) {
    const url_a = url.split('/')
    let name = url_a[url_a.length - 1]
    name += '.zip'
    return name
  } else {
    return url
  }
}

export const getLanguageIdByCode = (code) => {
  const _temp = LANGUAGES.filter((language) => language.code.toLowerCase() === code.toLowerCase())
  return _temp.length > 0 ? _temp[0].id : 1
}

export const getAssetNameById = (assets, assetId, languageId = 1) => {
  const selectedAsset = assets.filter((asset) => asset.asset === assetId)[0] || null
  if (selectedAsset) {
    let selectedLanguage =
      selectedAsset.description.filter((desc) => desc.language_id === languageId)[0] || null
    if (selectedLanguage) {
      return selectedLanguage.name
    }
  }

  return null
}

export const bytesToSize = (bytes) => {
  if (isNaN(bytes)) {
    return bytes
  }
  var sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  if (bytes == 0) return '0 Byte'
  var i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)))
  return Math.round(bytes / Math.pow(1024, i), 2) + ' ' + sizes[i]
}
export const sizeToBytes = (size) => {
  const splitString = (value) => value.split('')
  const getOnlyNumbers = (value) => value.filter((val) => /([0-9])/.test(val))
  const getUnitSize = (value) => value.filter((val) => /([A-Z])/i.test(val))
  const joinArray = (value) => value.join('')
  const convertToInteger = (value) => parseInt(value)
  const convertToBytes = (value, unitSize) => {
    switch (unitSize.toUpperCase()) {
      case 'BYTES':
        return value
      case 'KB':
        return Math.round(value * Math.pow(1024, 1))
      case 'MB':
        return Math.round(value * Math.pow(1024, 2))
      case 'GB':
        return Math.round(value * Math.pow(1024, 3))
      case 'TB':
        return Math.round(value * Math.pow(1024, 4))
      default:
        //MB is default
        return Math.round(value * Math.pow(1024, 2))
    }
  }

  let number = convertToInteger(joinArray(getOnlyNumbers(splitString(size))))
  let unit = joinArray(getUnitSize(splitString(size)))
  let bytes = convertToBytes(number, unit)

  return bytes
}

export const getFormattedDate = (_date) => {
  const date = new Date(_date)
  const year = date.getFullYear()
  const month = date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1
  const day = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate()
  const hours = date.getHours() < 10 ? `0${date.getHours()}` : date.getHours()
  const minutes = date.getMinutes() < 10 ? `0${date.getMinutes()}` : date.getMinutes()
  const seconds = date.getSeconds() < 10 ? `0${date.getSeconds()}` : date.getSeconds()

  return _date ? `${year}-${month}-${day} ${hours}:${minutes}:${seconds}` : ''
}

export async function urlToBlob(url) {
  let blob = await fetch({
    method: 'GET',
    body: url,
    headers: { Authorization: `Bearer ${getAccessToken()}` }
  }).then((r) => r.blob())
  return blob
}

export function b64toBlob(b64Data, contentType, sliceSize) {
  contentType = contentType || ''
  sliceSize = sliceSize || 512

  var byteCharacters = atob(b64Data)
  var byteArrays = []

  for (var offset = 0; offset < byteCharacters.length; offset += sliceSize) {
    var slice = byteCharacters.slice(offset, offset + sliceSize)

    var byteNumbers = new Array(slice.length)
    for (var i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i)
    }

    var byteArray = new Uint8Array(byteNumbers)

    byteArrays.push(byteArray)
  }

  var blob = new Blob(byteArrays, { type: contentType })

  return blob
}

export const getBase64MimeType = (encoded) => {
  let result = null

  if (typeof encoded !== 'string') {
    return result
  }

  let mime = encoded.match(/data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+).*,.*/)

  if (mime && mime.length) {
    result = mime[1]
  }

  return result
}

// const callback = (base64) => {
//   const URL = base64;
//   let block = URL.split(";")
//   let realData = block[1].split(",")[1]
//   let contentType = block[0].split(":")[1]
//   let MultiForm = new FormData()
//   return MultiForm.append("image", b64toBlob(realData, contentType))
// }

export const getRealDataMultiFormBase64WithParams = (data, paramName) => {
  const URL = data
  let block = URL.split(';')
  let realData = block[1].split(',')[1]
  let contentType = block[0].split(':')[1]
  let MultiForm = new FormData()
  MultiForm.append(paramName, b64toBlob(realData, contentType))

  return MultiForm
}

export const getRealDataMultiFormBase64 = (data) => {
  const URL = data
  let block = URL.split(';')
  let realData = block[1].split(',')[1]
  let contentType = block[0].split(':')[1]
  let MultiForm = new FormData()
  MultiForm.append('image', b64toBlob(realData, contentType))

  return MultiForm
}

export const getRealDataMultiForm = async (url) => {
  const ext = (url) => {
    return (url = url.substr(1 + url.lastIndexOf('/')).split('?')[0])
      .split('#')[0]
      .substr(url.lastIndexOf('.'))
  }

  const data = await fetch(url + '?not-from-cache-please')
  const blob = await data.blob()
  let MultiForm = new FormData()
  MultiForm.append('image', blob, ext(url))

  return MultiForm
  // const contentTypeExt = ext(data)
  // const image = new Image();
  // image.crossOrigin = "Anonymous";
  // image.src = data + "?not-from-cache-please";
  // image.onload = function(){
  //   const width = image.width;
  //   const height = image.height;

  //   const canvas = document.createElement('canvas');
  //   canvas.width = width;
  //   canvas.height = height;
  //   const ctx = canvas.getContext('2d');
  //   ctx.drawImage(image, 0, 0);
  //   const base64 = canvas.toDataURL({format: contentTypeExt})

  //   callback(base64)
  // }
}

export const validateObjectComplete = (obj, attRequired) => {
  let isValid = true
  if (!obj || !attRequired) return false

  for (const [key, value] of Object.entries(attRequired)) {
    if (isValid === false) break

    switch (value) {
      case 'string':
        isValid = obj[key] ? typeof obj[key] === 'string' && obj[key].length > 0 : false
        break
      case 'number':
        isValid = obj[key] !== null || obj[key] !== undefined ? typeof obj[key] === 'number' : false
        break
      case 'array':
        isValid = obj[key] ? Array.isArray(obj[key]) && obj[key].length > 0 : false
        break
      default:
        break
    }
  }

  return isValid
}

export const validateUrlBrightCove = (url, rule) => {
  // rule for short url bc
  // /(https:\/\/bcove.video\/)([A-Za-z0-9]{7})/
  let ruleToTest = rule
    ? rule
    : /^\/?((https:\/\/)?(\/\/)?players.brightcove.net)((\/\w+)*\/)([\w\-\.]+[^#?\s]+)(\?videoId=[0-9]+)$/
  const RULE_BRIGHT_CODE = new RegExp(ruleToTest)
  const result = RULE_BRIGHT_CODE.test(url)

  return result
}

export const handlerRedirectNewsletter = (lang) => {
  switch (lang) {
    case 'fr':
      return 'https://techprovider.intel.com/fr-fr-marketing-studio-sign-up'
    case 'es':
      return 'https://techprovider.intel.com/es-es-marketing-studio-sign-up'
    case 'it':
      return 'https://techprovider.intel.com/it-it-marketing-studio-sign-up'
    case 'de':
      return 'https://techprovider.intel.com/de-de-marketing-studio-sign-up'
    case 'tw':
      return 'https://techprovider.intel.com/tw-zh-marketing-studio-sign-up'
    case 'ja':
      return 'https://techprovider.intel.com/jp-ja-marketing-studio-sign-up'
    case 'ko':
      return 'https://techprovider.intel.com/kr-ko-marketing-studio-sign-up'
    case 'pt':
      return 'https://techprovider.intel.com/br-pt-marketing-studio-sign-up'
    case 'ex':
      return 'https://techprovider.intel.com/xl-es-marketing-studio-sign-up'
    default:
      return 'https://techprovider.intel.com/us-en-marketing-studio-sign-up'
  }
}

const hasEnglishMessage = (embargoMessages) => {
  const englishMessage = embargoMessages.find(
    (message) => message.language_id === englishLanguageId
  )

  return englishMessage && englishMessage.message.trim() !== ''
}

export const shouldAssetShow = (asset, component) => {
  // // // Condiciones según UN-1737 // // //
  let showRibbon = false,
    showEmbargoMessage = false,
    showRestrictionMessage = false

  const date = asset.restriction_date ? asset.restriction_date !== '' : false
  const message = asset.description_embargo
    ? asset.description_embargo.length > 0 && hasEnglishMessage(asset.description_embargo)
    : false
  const viewingPermissions = asset.viewing_permissions
    ? asset.viewing_permissions !== 'Restriction' && asset.viewing_permissions !== ''
    : false

  if ((date && viewingPermissions) || date) {
    showRibbon = isFuture(asset.restriction_date) ? true : false
    showEmbargoMessage = isFuture(asset.restriction_date) ? message : false
  }

  if (message && (!viewingPermissions || (!date && viewingPermissions))) {
    showRestrictionMessage = true
  }

  switch (component) {
    case 'ribbon':
      return showRibbon
    case 'embargoMessage':
      return showEmbargoMessage
    case 'restrictionMessage':
      return showRestrictionMessage
  }
}

export const isFuture = (date) => {
  // Returns whether a date 'Y-MM-DD HH:mm:ss' is in the future or not, comparing datetimes in UTC-0
  // Se acordó que el restriction date que se coloca desde el admin sea en GMT, por lo que hay que comparar la hora local con esa zona horaria.
  let restrictionDate = moment.utc(date) // restriction date SETEADA en UTC-0. Como viene sin información de zona horaria, usando utc() queda SETEADA en UTC-0.
  let now = moment.utc(new Date()) // Hora local CONVERTIDA a UTC-0. new Date() sí devuelve la fecha con la zona horaria, por lo que se CONVIERTE a UTC-0.
  return restrictionDate > now
}

export const isCampaignEmbargoed = (campaign) => {
  // // // Condición según UN-1868 // // //
  const date = campaign.restriction_date ? campaign.restriction_date !== '' : false

  return date && isFuture(campaign.restriction_date)
}

export const fetchPreferencesUser = () =>
  new Promise((resolve, reject) => {
    fetch(`${API_URL}/userConfig`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${getAccessToken()}`
      }
    })
      .then((data) => data.json())
      .then((response) => {
        resolve(response)
      })
      .catch((err) => reject(err))
  })

export const checkResourcePermission = (resource) =>
  new Promise((resolve, reject) => {
    fetch(`${API_URL}/assetUsertypes`, {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${getAccessToken()}`
      },
      method: 'POST',
      body: JSON.stringify({
        assetusertypes: `Resources -> ${resource}`
      })
    })
      .then((data) => data.json())
      .then((response) => {
        if (response.result && response.result.Resources) {
          resolve(response.result.Resources[resource])
        }
      })
      .catch((err) => reject(err))
  })

export const checkResourcePermissionByRole = (role) =>
  new Promise((resolve, reject) => {
    fetch(`${API_URL}/checkrole?role=${role}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${getAccessToken()}`
      }
    })
      .then((data) => data.json())
      .then((response) => {
        if (response) {
          resolve(response.is_role)
        }
      })
      .catch((err) => reject(err))
  })

export const canSeeToolBar = () =>
  new Promise((resolve, reject) => {
    fetch(`${API_URL}/isAdmitLogin`, {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${getAccessToken()}`
      },
      method: 'GET'
    })
      .then((data) => data.json())
      .then((response) => {
        // Ocultado temporal de testing tool
        // resolve(false)
        // Descomentar para mostrar testing tool
        if (response.data) {
          resolve(response.data)
        }
      })
      .catch((err) => reject(err))
  })

export const viewsReducedExperience = (ipaRoles) => {
  // // Habilita la experiencia reducida del studio según el rol del usuario. // //

  // let roles = ipaRoles && ipaRoles.toLowerCase()
  // return roles === "fpga"
  return false
}

export const hasPermissionToBrandAssets = (roles) => {
  const rolesAllowedInBrandAssets = [
    'IIPAM',
    'CCF',
    'DCF',
    'SPCF',
    'Solution Provider',
    'FPGA',
    'ISV',
    'OEM',
    'Manufacturer',
    'Service Integrator',
    'Service Provider',
    'Distributor',
    'SOEM'
  ]
  const userRoles = roles ? roles.split(',') : []
  const hasPremission = userRoles.some((userRole) => {
    return rolesAllowedInBrandAssets.some(
      (allowedRole) => userRole.trim().toUpperCase() === allowedRole.trim().toUpperCase()
    )
  })

  return hasPremission
}

export const belongsToUiLanguages = (languageId) => {
  const uiLanguagesIds = [1, 4, 5, 6, 7, 8, 9, 11, 2, 12, 3, 10, 13, 15, 14, 16, 17]
  return uiLanguagesIds.includes(languageId)
}

export const copyToClipboard = (text) => {
  try {
    let dummy = document.createElement('textarea')
    document.body.appendChild(dummy)
    dummy.value = text
    dummy.select()
    document.execCommand('copy')
    document.body.removeChild(dummy)
    return true
  } catch (error) {
    return false
  }
}

export const getNewUrl = (pathname, searchString, appliedFilters, campaignId) => {
  let type = 0
  switch (pathname.split('/')[1]) {
    case 'assets':
      type = 1
      break
    case 'campaign':
      type = 1
      break
    case 'campaigns':
      type = 2
      break
    case 'resultsSearch':
      type = 0
      break
    default:
      type = 0
  }

  return `${pathname}?type=${type}${searchString}${appliedFilters}`
}

export const analyzeUrl = (locationSearch) => {
  const values = queryString.parse(locationSearch, {
    parseNumbers: true,
    parseBooleans: true,
    arrayFormat: 'bracket'
  })

  let searchString = values.search ? decodeURIComponent(values.search) : null
  let filtersApplied = values.filters
    ? typeof values.filters === 'string'
      ? JSON.parse(decodeURIComponent(values.filters))
      : values.filters
    : null
  let materialType = values.type ? parseInt(values.type) : 0
  let materialPage =
    values.assetPage || values.campaignPage ? values.assetPage || values.campaignPage : ''

  return { searchString, filtersApplied, materialType, materialPage }
}

export const encodeKeyword = (paramName, keyword) => {
  let urlSearchParam = new URLSearchParams({ [paramName]: keyword }).toString()
  return `&${urlSearchParam}`
}

export const getAnalyticsCode = (langCode) => {
  const analyticCodes = {
    en: 'en-us',
    fr: 'fr-fr',
    de: 'de-de',
    id: 'in-id',
    it: 'it-it',
    ja: 'ja-jp',
    ko: 'ko-kr',
    pl: 'pl-pl',
    br: 'pt-br',
    // ru: 'ru-ru', // refactor: THIS FUNCTION IS NOT USED ANYMORE THOUGH
    cn: 'zh-cn',
    ex: 'es-mx',
    es: 'es-es',
    th: 'th-th',
    tw: 'zh-tw',
    tr: 'tr-tr',
    vn: 'vi-vn'
  }

  return analyticCodes[langCode] || 'en-us'
}

export const getCookiesPermissions = () => {
  let consent = localStorage.getItem('_wap_user_consent_str')
  if (consent === null) consent = localStorage.getItem('_wap_default_user_consent_str')
  return String(consent)
    .split(',')
    .reduce(
      (acc, attr) =>
        Object.defineProperty(acc, attr, {
          value: true,
          writable: false,
          enumerable: false,
          configurable: false
        }),
      {}
    )
}

export const localStorageLanguage = {
  set: (value) => {
    const keys = getCookiesPermissions()
    if (keys.functional) {
      localStorage.setItem('lang', value)
    }
  },
  get: () => {
    const keys = getCookiesPermissions()
    if (keys.functional) {
      return localStorage.getItem('lang')
    }
    localStorageLanguage.remove()
    return null
  },
  remove: () => localStorage.removeItem('lang')
}

export const sessionStorageShowNewsletter = {
  set: (value) => {
    const keys = getCookiesPermissions()
    if (keys.functional) {
      sessionStorage.setItem('showNewsletter', value)
    }
  },
  get: () => {
    const keys = getCookiesPermissions()
    if (keys.functional) {
      return sessionStorage.getItem('showNewsletter')
    }
    sessionStorageShowNewsletter.remove()
    return null
  },
  remove: () => sessionStorage.removeItem('showNewsletter')
}

export const sessionStorageRule50Items = {
  set: (value) => {
    const keys = getCookiesPermissions()
    if (keys.functional) {
      sessionStorage.setItem('rule50items', value)
    }
  },
  get: () => {
    const keys = getCookiesPermissions()
    if (keys.functional) {
      return sessionStorage.getItem('rule50items')
    }
    sessionStorageRule50Items.remove()
    return null
  },
  remove: () => sessionStorage.removeItem('rule50items')
}

export const sessionStorageTooltip = {
  set: (key) => {
    const keys = getCookiesPermissions()
    if (keys.functional) {
      sessionStorage.setItem(`tooltip-${key}`, 'user-accepted')
    }
  },
  get: (key) => {
    const keys = getCookiesPermissions()
    if (keys.functional) {
      return sessionStorage.getItem(`tooltip-${key}`)
    }
    sessionStorageTooltip.removeAll()
    return null
  },
  removeAll: () => {
    const keys = Object.keys(sessionStorage).filter((k) => k.includes('tooltip-'))
    keys.forEach((k) => sessionStorage.removeItem(k))
  }
}

export const getRatio = (width, height) => width / height

export const convertToInches = (unit, value) => {
  switch (unit.toLowerCase()) {
    case 'milimeters':
      return value / 25.4
    case 'centimeters':
      return value / 2.54
    case 'meters':
      return value * 39.3701
    case 'feet':
      return value * 12
    default:
      return value
  }
}

export const inchConverter = (value, unit) => {
  switch (unit.toLowerCase()) {
    case 'centimeters':
      return value * 2.54
    case 'meters':
      return value * 0.0254
    case 'feet':
      return value * 0.0833333
    default:
      return value
  }
}

// Calculation B
export const getMemberSquareWeight = (memberRatio) => {
  const MIN = 0.07
  const memberSquareWeight =
    Math.abs(1 - memberRatio) < MIN ? (MIN - Math.abs(1 - memberRatio)) / MIN : 0
  return memberSquareWeight
}

// Calculation C
export const getIntelSquareWeight = (intelRatio) => {
  const MIN = 0.4
  const intelSquareWeight =
    Math.abs(1 - intelRatio) < MIN ? (MIN - Math.abs(1 - intelRatio)) / MIN : 0
  return intelSquareWeight
}

// Calculation A
export const getAspectWeight = (memberRatio, intelRatio) => {
  const memberRatioCalc = 1 - memberRatio
  const intelRatioCalc = 1 - intelRatio

  const memberRatioMult = memberRatioCalc * -1

  const aspectWeight = memberRatioMult + intelRatioCalc

  return Math.abs(aspectWeight)
}

// Calculation D
export const getIntelLongRectWeight = (intelRatio) => {
  const intelLongRectWeight = intelRatio > 3 ? (intelRatio - 3) / 10 : 0
  return intelLongRectWeight
}

// Calculation E
export const getBalancedArea = (
  memberWidth,
  memberHeight,
  aspectWeight,
  memberSquareWeight,
  intelSquareWeight,
  intelLongRectWeight
) => {
  const calcBalanced = memberWidth * memberHeight * 0.87

  const calcAspect = 1 - (aspectWeight * 4.5) / 100

  const intelSquare =
    1 - memberSquareWeight * 0.12 + intelSquareWeight * 0.2 + intelLongRectWeight * 0.4

  return calcBalanced * (calcAspect * intelSquare)
}

// Calculation F
export const getBalencedWidth = (balencedArea, intelMinwidth, intelMinheight) => {
  const intelWidth = intelMinwidth * intelMinheight
  const intelSquare = balencedArea / intelWidth

  const squareRoot = Math.sqrt(intelSquare) * intelMinwidth

  return squareRoot
}

// Calculation G
export const getBalencedHeight = (balencedArea, intelMinwidth, intelMinheight) => {
  const intelWidth = intelMinwidth * intelMinheight
  const intelSquare = balencedArea / intelWidth

  const squareRoot = Math.sqrt(intelSquare) * intelMinheight

  return squareRoot
}

const token = getAccessToken()

function GET(path, callback) {
  return fetchData(path, callback, 'GET', null)
}

function POST(path, callback, body) {
  return fetchData(path, callback, 'POST', body)
}

function POSTUNZIPPER(path, callback, body, staticURL) {
  return fetchData(path, callback, 'POST', body, staticURL)
}

function PUT(path, callback, body) {
  return fetchData(path, callback, 'PUT', body)
}

function PATCH(path, callback, body) {
  return fetchData(path, callback, 'PATCH', body)
}

function DELETE(path, callback, body) {
  return fetchData(path, callback, 'DELETE', body)
}

function GETUNZIPPER(path, callback, body, staticURL) {
  return fetch(!staticURL ? `${API_URL}${path}` : path, {
    method: 'GET',
    body: body ? JSON.stringify(body) : null
  })
    .then((data) => data.json())
    .then((dataJson) => callback(dataJson))
    .catch((error) => callback(error))
}

function fetchData(path, callback, method, body, staticURL) {
  const headers = {
    Accept: 'application/json',
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`
  }

  return fetch(!staticURL ? `${API_URL}${path}` : path, {
    method,
    body: body ? JSON.stringify(body) : null,
    headers
  })
    .then((data) => data.json())
    .then((dataJson) => callback(dataJson))
    .catch((error) => callback(error))
}

export const Fetch = {
  PUT,
  GET,
  POST,
  PATCH,
  DELETE,
  POSTUNZIPPER,
  GETUNZIPPER
}
