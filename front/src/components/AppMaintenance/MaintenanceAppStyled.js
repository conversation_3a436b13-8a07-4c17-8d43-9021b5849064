import styled from 'styled-components'
import backgroundImg from 'src/images/rs-bg-head-dt.jpg'

export const MaintenanceAppStyled = styled.div`
  background: url(${backgroundImg}) no-repeat;
  background-size: cover;
  min-height: calc(100vh - 70px);

  .studio-logo {
    position: absolute;
    top: 65px;
    left: 91px;
  }

  .maintenance-section {
    background-color: #f4f4f4;
    width: 50%;
    margin-left: auto;
    height: fit-content;
    min-height: inherit;
    padding: 60px 0 0 75px;

    .alert-container {
      padding: 24px 75px 60px 0px;

      h2 {
        max-width: 470px;
      }

      p {
        line-height: normal;
        margin-bottom: 30px;
        max-width: 390px;

        a {
          color: #0068b5;
        }
      }
      .maintenance-banner-links {
        list-style-type: square;
        li::marker {
          color: #0046c8;
        }
        li p {
          margin: 0 0 10px 0;
        }
      }
    }
  }

  @media (max-width: 1024px) {
    .maintenance-section {
      padding: 40px 0 0 60px;

      .alert-container {
        padding: 24px 60px 40px 0px;

        h2 {
          font-size: 40px !important;
        }
        p {
          font-size: 18px;
        }
      }
    }
  }

  @media (max-width: 890px) {
    .maintenance-section {
      padding: 40px 0 0 30px;

      .alert-container {
        padding: 24px 30px 40px 0px;
      }
    }
  }

  @media (max-width: 767px) {
    padding-top: 190px;
    min-height: unset;
    background-size: 160%;

    .studio-logo {
      left: 41px;
    }

    .maintenance-section {
      width: 100%;
      margin-left: 0;
      padding: 50px 0 0 40px;

      .alert-container {
        padding: 24px 40px 30px 0px;

        h2 {
          font-size: 36px !important;
        }
        p {
          font-size: 18px;
        }
      }
    }
  }

  .maintenance-section.ja .alert-container {
    h2 {
      font-size: 42px !important;
      @media (max-width: 1024px) {
        font-size: 30px !important;
      }
      @media (max-width: 767px) {
        font-size: 26px !important;
      }
    }
    p {
      @media (max-width: 1024px) {
        font-size: 16px !important;
      }
      @media (max-width: 767px) {
        font-size: 14px !important;
      }
    }
  }
`
