import { useEffect, useState } from 'react'
import { englishLanguageId, LANGUAGES } from 'src/v2/constants/languages'
import { localStorageLanguage, getLanguageCodeById } from 'src/helpers'

export const useGetMaintenanceInfo = (serviceBanner: any, uiLanguage: number) => {
  const [maintenanceData, setMaintenanceData] = useState(null)
  const [maintenanceInfo, setMaintenanceInfo] = useState(null)

  useEffect(() => {
    if (serviceBanner?.content) {
      setMaintenanceData(serviceBanner?.content)
    }
  }, [serviceBanner])

  useEffect(() => {
    if (maintenanceData) {
      const newInfo = getContentTranslation(uiLanguage)
      setMaintenanceInfo(newInfo.json_beautified[0].banner)
    }
  }, [maintenanceData])

  const getContentTranslation = (langId: number) => {
    const UiContent = maintenanceData.find((content: any) => content.language_id === langId)
    const EnglishContent = maintenanceData.find(
      (content: any) => content.language_id === englishLanguageId
    )

    return UiContent || EnglishContent
  }

  const changeMaintenanceInfoLanguage = (langId: number) => {
    const languageCode = getLanguageCodeById(langId)
    const selectedLanguage = LANGUAGES.find((language) => language.id === langId)

    // Update localStorageLanguage (key: 'lang' or 'lang_fallback')
    localStorageLanguage.set(languageCode)

    // Update localStorage 'language' key
    if (selectedLanguage?.wapLocalCode) {
      const activeCulturesString =
        (window as any).INTELNAV?.Cultures?.unav?.ActiveCulturesFramework || ''
      const activeCultures = activeCulturesString
        ? activeCulturesString.split('|').map((culture: string) => culture.split(':')[0])
        : []

      if (activeCultures.includes(selectedLanguage.wapLocalCode)) {
        localStorage.setItem('language', selectedLanguage.wapLocalCode)
      } else {
        localStorage.setItem('language', 'us_EN')
      }
    } else {
      localStorage.setItem('language', 'us_EN')
    }

    const newInfo = getContentTranslation(langId)
    setMaintenanceInfo(newInfo.json_beautified[0].banner)
  }

  return {
    maintenanceInfo,
    changeMaintenanceInfoLanguage
  }
}
