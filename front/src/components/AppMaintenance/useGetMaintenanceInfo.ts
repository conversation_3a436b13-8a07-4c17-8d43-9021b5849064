import { useEffect, useState } from 'react'
import { englishLanguageId } from 'src/v2/constants/languages'
import { localStorageLanguage, getLanguageCodeById } from 'src/helpers'

export const useGetMaintenanceInfo = (serviceBanner: any, uiLanguage: number) => {
  const [maintenanceData, setMaintenanceData] = useState(null)
  const [maintenanceInfo, setMaintenanceInfo] = useState(null)

  useEffect(() => {
    if (serviceBanner?.content) {
      setMaintenanceData(serviceBanner?.content)
    }
  }, [serviceBanner])

  useEffect(() => {
    if (maintenanceData) {
      const newInfo = getContentTranslation(uiLanguage)
      setMaintenanceInfo(newInfo.json_beautified[0].banner)
    }
  }, [maintenanceData])

  const getContentTranslation = (langId: number) => {
    const UiContent = maintenanceData.find((content: any) => content.language_id === langId)
    const EnglishContent = maintenanceData.find(
      (content: any) => content.language_id === englishLanguageId
    )

    return UiContent || EnglishContent
  }

  const changeMaintenanceInfoLanguage = (langId: number) => {
    console.log('change maintenance info language - LANG-ID:', langId)

    const languageCode = getLanguageCodeById(langId)
    localStorageLanguage.set(languageCode)

    const newInfo = getContentTranslation(langId)
    setMaintenanceInfo(newInfo.json_beautified[0].banner)
  }

  return {
    maintenanceInfo,
    changeMaintenanceInfoLanguage
  }
}
