import { useState, useEffect, useMemo } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { setLanguage } from 'redux-i18n'

import httpFetch from 'src/v2/intances/FetchHttp'
import AdobeAnalytics from 'src/v2/intances/AdobeAnalytics'
import { LANGUAGES, englishLanguageCode } from 'src/v2/constants/languages'

import { userServices } from 'src/v2/services/UserServices'

import { fetchSetPreferedLanguageSuccess } from '../../../../actions/fetchActions'
import { updateSelectedLanguage } from 'src/v2/storage/actionCreators/userConfig.action'

import { localStorageLanguage } from 'src/helpers'
import { hasPrivateToken } from 'src/v2/helpers/cookiesHelpers'
import { getLanguageCodeById, getLanguageIdByCode } from 'src/v2/helpers/languageHelper'
import { changeRootCssClass } from 'src/v2/helpers/domHelpers'

declare global {
  interface Window {
    INTELNAV?: {
      Cultures?: {
        unav?: {
          ActiveCulturesFramework?: string
        }
      }
    }
  }
}

const { changeSelectedLanguage } = userServices(httpFetch)
const { eventDispatch } = AdobeAnalytics

interface useUILanguageSelectorParams {
  callback?: (languageId: number) => void
  initialLanguageId?: number
}

const useUILanguageSelector = ({ callback, initialLanguageId }: useUILanguageSelectorParams) => {
  const privateToken = hasPrivateToken()
  const [firstPreferencesLoad, setFirstPreferencesLoad] = useState(true)
  const [selectedLangId, setSelectedLangId] = useState(null)

  const activeCultures = useMemo(() => {
    const activeCulturesString = window.INTELNAV?.Cultures?.unav?.ActiveCulturesFramework || ''
    if (!activeCulturesString) return []
    const activeCulturesArray = activeCulturesString.split('|')
    return activeCulturesArray.map((culture: string) => culture.split(':')[0])
  }, [])

  const preferedUiLanguageId = useSelector(
    (state: any) => state?.reducer?.preferences?.ui_language_id
  ) // TO DO: Migrar a nuevo store, se seteará al entrar a PrivateRouter

  const dispatch: any = useDispatch()

  useEffect(() => {
    if (firstPreferencesLoad) {
      const uiLanguageId =
        initialLanguageId ||
        (privateToken
          ? preferedUiLanguageId
          : getLanguageIdByCode(localStorageLanguage.get() || englishLanguageCode)) // TO DO: Migrar a nuevo helper de local store en refactor de router

      if (uiLanguageId) {
        /** Change dropdown selected Language */
        setSelectedLangId(uiLanguageId)

        /** Sets redux-i18n language */
        dispatch(setLanguage(getLanguageCodeById(uiLanguageId))) // TO DO: De esto se encargarán los hooks de inicio de APP en nuevo Router

        changeRootCssClass(uiLanguageId) // TO DO: De esto se encargarán los hooks de inicio de APP en nuevo Router

        setFirstPreferencesLoad(false)
      }
    }
  }, [preferedUiLanguageId, firstPreferencesLoad, initialLanguageId])

  // Sync with external language changes
  useEffect(() => {
    if (initialLanguageId && initialLanguageId !== selectedLangId && !firstPreferencesLoad) {
      setSelectedLangId(initialLanguageId)
    }
  }, [initialLanguageId, selectedLangId, firstPreferencesLoad])

  const handleLanguageChange = async (langId: number) => {
    /** Change dropdown selected Language */
    setSelectedLangId(langId)

    /** Update local storage language */
    const selectedLanguage = LANGUAGES.find((language) => language.id === langId) // TO DO: Quitar al migrar a 'lang'
    localStorage.setItem('langObj', JSON.stringify(selectedLanguage)) // TO DO: MIGRAR SUS USOS A 'LANG'

    localStorageLanguage.set(getLanguageCodeById(langId))

    /** Set wapLocalCode in localStorage */
    if (selectedLanguage.wapLocalCode) {
      localStorage.setItem('language', selectedLanguage.wapLocalCode)
    }

    // set active cultures
    if (activeCultures.includes(selectedLanguage?.wapLocalCode)) {
      localStorage.setItem('language', selectedLanguage.wapLocalCode)
    } else {
      localStorage.setItem('language', 'us_EN')
    }

    /** Set redux-i18n language */
    dispatch(setLanguage(getLanguageCodeById(langId)))

    /** Set user preferences in old redux */
    dispatch(fetchSetPreferedLanguageSuccess(langId)) // TO DO: Quitar cuando se use el nuevo store

    /** Set user preferences in new redux */
    dispatch(updateSelectedLanguage(langId))

    /** Change user preferences in backend */
    if (privateToken) {
      await changeSelectedLanguage(langId)
    }

    changeRootCssClass(langId)

    eventDispatch({
      action: 'click',
      category: 'nav_bar',
      label: `${!privateToken && 'guest_'}navbar_lang_${getLanguageCodeById(langId)}`
    })

    callback?.(langId)

    /** Reload the page to apply language changes */
    window.location.reload()
  }

  return { selectedLangId, handleLanguageChange }
}

export default useUILanguageSelector
