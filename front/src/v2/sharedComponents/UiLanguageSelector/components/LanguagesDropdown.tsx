import React, { useState, useRef } from 'react'
import { UILANGUAGES } from 'src/v2/constants/languages'
import { getDescriptionLanguageById, sortByNameWithEnglishFirst } from 'src/v2/helpers/languageHelper'
import { LanguagesDropdownStyled } from './LanguagesDropdownStyled'
import useHandleClickOutside from 'src/v2/hooks/useHandleClickOutside'

interface LanguagesDropdownProps {
  selectedLanguageId: number
  languageChangeHandler: (languageId: number) => Promise<void>
  isPublic?: boolean
}

const LanguagesDropdown = ({ selectedLanguageId, languageChangeHandler, isPublic = false }: LanguagesDropdownProps) => {
  const languageSelectorRef = useRef()
  const uiLanguagesList = sortByNameWithEnglishFirst(UILANGUAGES)
  const selectedLanguageName = getDescriptionLanguageById(selectedLanguageId)

  const [isOpen, setIsOpen] = useState(false)

  useHandleClickOutside({ ref: languageSelectorRef, callback: () => setIsOpen(false) })

  const handleClick = (languageId: number) => {
    setIsOpen(!isOpen)
    void languageChangeHandler(languageId)
  }

  return (
    <LanguagesDropdownStyled isOpen={isOpen} isPublic={isPublic} ref={languageSelectorRef}>
      <div className='selected-language' onClick={() => setIsOpen(!isOpen)}>
        {selectedLanguageName === 'English' ? 'USA (English)' : selectedLanguageName}
      </div>
      <div className='languages-list'>
        <ul>
          {uiLanguagesList.map((languageItem, index) => (
            <li key={index} onClick={() => handleClick(languageItem.id)}>
              {languageItem.description === 'English' ? 'USA (English)' : languageItem.description}
            </li>
          ))}
        </ul>
      </div>
    </LanguagesDropdownStyled>
  )
}

export default LanguagesDropdown
