import React from 'react'

import useUILanguageSelector from './hooks/useUILanguageSelector'
import LanguagesDropdown from './components/LanguagesDropdown'

interface UiLanguageSelectorProps {
  id?: string
  styles?: React.CSSProperties
  languageChangeCallback?: (langId: number) => void
  isPublic?: boolean
  selectedLanguageId?: number
}

const UiLanguageSelector = ({ id, styles, languageChangeCallback, isPublic = false, selectedLanguageId }: UiLanguageSelectorProps) => {
  const { selectedLangId, handleLanguageChange } = useUILanguageSelector({
    callback: languageChangeCallback,
    initialLanguageId: selectedLanguageId
  })

  return (
    <div id={id} style={{ width: 'fit-content', ...styles }}>
      <LanguagesDropdown
        isPublic={isPublic}
        selectedLanguageId={selectedLangId}
        languageChangeHandler={async (langId: number) => await handleLanguageChange(langId)}
      />
    </div>
  )
}

export default UiLanguageSelector
