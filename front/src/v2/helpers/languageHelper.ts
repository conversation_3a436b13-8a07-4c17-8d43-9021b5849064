import { LANGUAGES, LanguagesListItem, englishLanguageId } from 'src/v2/constants/languages'
import { getSortByKeyword } from './sorts'

export const getDescriptionLanguageById = (id: number) => {
  const selectedLanguage = LANGUAGES.find((language) => language.id === id)
  if (selectedLanguage) {
    return selectedLanguage.description
  }
  return ''
}

export const getLanguageIdByCode = (code: string) => {
  // if (!code) {
  //   return 1 // default: english lang ID
  // }

  const selectedLanguage = LANGUAGES.filter(
    (language) => language.code?.toLowerCase() === code?.toLowerCase()
  )
  return selectedLanguage.length > 0 ? selectedLanguage[0].id : 1
}

export const getLanguageCodeById = (id: number) => {
  const selectedLanguage = LANGUAGES.filter((language) => language.id === id)
  return selectedLanguage.length > 0 ? selectedLanguage[0].code : 'en'
}

export const sortByNameWithEnglishFirst = (uiLanguages: LanguagesListItem[]) => {
  const sortedLanguages = getSortByKeyword(uiLanguages, 'description', 'DESC')

  const englishIndex = sortedLanguages.findIndex((lang) => lang.id === englishLanguageId)
  const englishLanguage = sortedLanguages.splice(englishIndex, 1)[0]

  sortedLanguages.unshift(englishLanguage)
  return sortedLanguages
}
