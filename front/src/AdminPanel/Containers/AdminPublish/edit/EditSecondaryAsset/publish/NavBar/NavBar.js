import React, { Component } from 'react'
import homepageLogo from '../../../../../../../images/rs-icon-home.svg'
import LanguagesAndHelpContainer from 'src/v2/sharedComponents/NavBars/components/LanguagesAndHelpContainer'
import { LanguagesDropdownStyled } from 'src/v2/sharedComponents/UiLanguageSelector/components/LanguagesDropdownStyled'

class NavBar extends Component {
  render() {
    return (
      <>
        <header className="marketing-studio-header">
          <div className="msh-content">
            <a
              href="https://www.intel.com/content/www/us/en/homepage.html"
              rel="noopener noreferrer"
              target="_blank"
              alt="Intel homepage"
              className="navbar-logo"
              title="Intel Logo"
            >
              &nbsp;
            </a>
          </div>
        </header>

        <div id="nav-menu" className="navbar">
          <div className={'d-flex container-navbar'}>
            <ul className={'mr-auto main-ul'}>
              <li title="Home" className={'home'}>
                <img src={homepageLogo} alt="home" />
              </li>
              <li title="My Account" className="nav-my-account">
                My Account
              </li>
              <li title="Campaigns" className="nav-my-campaign">
                {/* TODO: Cambiar por un boton */}
                <a rel="noopener noreferrer" href="#">
                  Campaigns
                </a>
              </li>
              <li title="Assets" className="nav-my-asset">
                {/* TODO: Cambiar por un boton */}
                <a rel="noopener noreferrer" href="#">
                  Assets
                </a>
              </li>
              <li title="Resources" className="nav-my-resource">
                Resources
              </li>
            </ul>

            <LanguagesAndHelpContainer>
              <LanguagesDropdownStyled isOpen={false} isPublic={false}>
                <div className="selected-language">English</div>
              </LanguagesDropdownStyled>

              <li title="Help" className="help-itm">
                Help
              </li>
            </LanguagesAndHelpContainer>
          </div>
        </div>
      </>
    )
  }
}

export default NavBar
